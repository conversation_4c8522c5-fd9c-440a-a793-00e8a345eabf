{% load humanize %}
<div class="content-area" id="content-area">
    <div class="content-header" id="content-header" hx-on::after-swap="alert('Ttitle swapped')">
        <span class="material-icons">arrow_back</span>
        <h2 class="page-title">
            <span x-text="pageTitle"></span>
            <!-- <span class="page-title-count" x-show="activeNav === 'students'" x-text="`(${filteredData.length})`"></span> -->
        </h2>
        <div class="actions">
            <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn">
                <span class="mdc-button__ripple"></span>
                <span class="material-icons" style="pointer-events: none;">add</span>
                <span class="mdc-button__label">Ajouter un élève</span>
            </button>
            <span class="material-icons hidden" title="Exporter vers Excel">description</span>
            <span class="material-icons hidden" title="Importer des données">file_upload</span>
            <span class="material-icons hidden" title="Changer de vue">view_module</span>
            <span class="material-icons search-icon-mobile" title="Rechercher" id="mobile-search-btn">search</span>
            <span class="material-icons" title="Filtrer" id="filter-btn">filter_list</span>
        </div>
    </div>

    <div  x-data="studentsApp()" x-init="initializeStudentsData()">
        <!-- Quick Filter Chips -->
        <div class="quick-filter-chips">
            <button class="quick-filter-chip active" data-filter="all" @click="setQuickFilter('all')">
                <span class="material-icons">group</span>
                <span>Tous</span>
            </button>
            <button class="quick-filter-chip" data-filter="paid" @click="setQuickFilter('paid')">
                <span class="material-icons">paid</span>
                <span>Payé</span>
            </button>
            <button class="quick-filter-chip" data-filter="no-payments" @click="setQuickFilter('no-payments')">
                <span class="material-icons">money_off</span>
                <span>Aucun paiement</span>
            </button>
            <button class="quick-filter-chip" data-filter="all-paid" @click="setQuickFilter('all-paid')">
                <span class="material-icons">check_circle</span>
                <span>Soldé</span>
            </button>
            <button class="quick-filter-chip" data-filter="enrolled-today" @click="setQuickFilter('enrolled-today')">
                <span class="material-icons">today</span>
                <span>Aujourd'hui</span>
            </button>
        </div>

        <!-- Filter Chips Container -->
        <div class="filter-chips-container" id="filter-chips-container">
            <!-- Filter chips will be dynamically added here -->
        </div>

        <!-- Data Table (Desktop) -->
        <div class="data-table-container">
            <!-- Targeted Loading Overlay for Table -->
            <div class="target-loading-overlay" id="table-loading-overlay">
                <div class="target-loading-content">
                    <div class="simple-spinner">
                        <svg class="spinner-svg" viewBox="0 0 50 50">
                            <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#1976d2" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                    </div>
                    <div class="target-loading-text" id="table-loading-text">Chargement...</div>
                </div>
            </div>

            <!-- Table Controls -->
            <div class="table-controls">
                <div class="search-container">
                    <span class="search-label">Rechercher:</span>
                    <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon mdc-small-input mdc-search-input">
                        <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                        <input type="text" class="mdc-text-field__input" id="table-search" placeholder="Rechercher des élèves..." x-model="searchQuery">
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__notch"></div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                </div>
                <div class="per-page-container">
                    <span class="per-page-label">Lignes par page:</span>
                    <div class="mdc-select mdc-select--outlined mdc-small-input" id="per-page-select">
                        <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                            <span class="mdc-select__selected-text-container">
                                <span class="mdc-select__selected-text" x-text="perPage"></span>
                            </span>
                            <span class="mdc-select__dropdown-icon">
                                <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                    <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                    <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                                </svg>
                            </span>
                            <div class="mdc-notched-outline">
                                <div class="mdc-notched-outline__leading"></div>
                                <div class="mdc-notched-outline__trailing"></div>
                            </div>
                        </div>
                        <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                            <ul class="mdc-deprecated-list" role="listbox">
                                <li class="mdc-deprecated-list-item" data-value="5" role="option">
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">5</span>
                                </li>
                                <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="10" role="option" aria-selected="true">
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">10</span>
                                </li>
                                <li class="mdc-deprecated-list-item" data-value="25" role="option">
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">25</span>
                                </li>
                                <li class="mdc-deprecated-list-item" data-value="50" role="option">
                                    <span class="mdc-deprecated-list-item__ripple"></span>
                                    <span class="mdc-deprecated-list-item__text">50</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- MDC Data Table -->
            <div class="mdc-data-table" x-show="filteredData.length > 0">
                <div class="mdc-data-table__table-container">
                    <table class="mdc-data-table__table" aria-label="Liste des élèves">
                        <thead>
                            <tr class="mdc-data-table__header-row">
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                    <div class="mdc-checkbox mdc-data-table__header-row-checkbox" :class="{ 'mdc-checkbox--selected': allVisibleSelected }">
                                        <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-checkbox" aria-label="Sélectionner toutes les lignes"
                                            :checked="allVisibleSelected"
                                            :indeterminate="someVisibleSelected && !allVisibleSelected"
                                            @change="selectAllStudents()">
                                        <div class="mdc-checkbox__background">
                                            <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                            </svg>
                                            <div class="mdc-checkbox__mixedmark"></div>
                                        </div>
                                        <div class="mdc-checkbox__ripple"></div>
                                    </div>
                                </th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Photo</th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                    :class="getSortClass('name')"
                                    role="columnheader" scope="col" data-column="name"
                                    @click="sortData('name')">
                                    Nom et Prénoms
                                    <span class="material-icons sort-icon" x-text="getSortIcon('name')"></span>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                    :class="getSortClass('student_id')"
                                    role="columnheader" scope="col" data-column="student_id"
                                    @click="sortData('student_id')">
                                    Matricule
                                    <span class="material-icons sort-icon" x-text="getSortIcon('student_id')"></span>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                    :class="getSortClass('level_fr')"
                                    role="columnheader" scope="col" data-column="level_fr"
                                    @click="sortData('level_fr')">
                                    Classe
                                    <span class="material-icons sort-icon" x-text="getSortIcon('level_fr')"></span>
                                </th>
                                {% if is_arabic_school %}
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                    :class="getSortClass('level_ar')"
                                    role="columnheader" scope="col" data-column="level_ar"
                                    @click="sortData('level_ar')">
                                    Arabe
                                    <span class="material-icons sort-icon" x-text="getSortIcon('level_ar')"></span>
                                </th>
                                {% endif %}
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                    :class="getSortClass('gender')"
                                    role="columnheader" scope="col" data-column="gender"
                                    @click="sortData('gender')">
                                    Sexe
                                    <span class="material-icons sort-icon" x-text="getSortIcon('gender')"></span>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                    :class="getSortClass('status')"
                                    role="columnheader" scope="col" data-column="status"
                                    @click="sortData('status')">
                                    Statut
                                    <span class="material-icons sort-icon" x-text="getSortIcon('status')"></span>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column"
                                    :class="getSortClass('debt')"
                                    role="columnheader" scope="col" data-column="debt"
                                    @click="sortData('debt')">
                                    Arriéré
                                    <span class="material-icons sort-icon" x-text="getSortIcon('debt')"></span>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column"
                                    :class="getSortClass('amount')"
                                    role="columnheader" scope="col" data-column="amount"
                                    @click="sortData('amount')">
                                    À payer
                                    <span class="material-icons sort-icon" x-text="getSortIcon('amount')"></span>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column"
                                    :class="getSortClass('paid')"
                                    role="columnheader" scope="col" data-column="paid"
                                    @click="sortData('paid')">
                                    Payé
                                    <span class="material-icons sort-icon" x-text="getSortIcon('paid')"></span>
                                </th>
                                <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column"
                                    :class="getSortClass('remaining')"
                                    role="columnheader" scope="col" data-column="remaining"
                                    @click="sortData('remaining')">
                                    Reste
                                    <span class="material-icons sort-icon" x-text="getSortIcon('remaining')"></span>
                                </th>
                                <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="mdc-data-table__content" id="table-body">
                            <!-- Alpine.js Table Rows Template -->
                            <template x-if="paginatedData.length === 0">
                                <tr class="mdc-data-table__row">
                                    <td class="mdc-data-table__cell" colspan="13" style="text-align: center; padding: 48px;">
                                        <div style="color: #757575;">
                                            <span class="material-icons" style="font-size: 48px; margin-bottom: 16px; display: block;">search_off</span>
                                            Aucun élève trouvé
                                        </div>
                                    </td>
                                </tr>
                            </template>

                            <template x-for="student in paginatedData" :key="student.id">
                                <tr class="mdc-data-table__row table-row"
                                    :class="{ 'mdc-data-table__row--selected': isStudentSelected(student.id) }"
                                    :data-student-id="student.id"
                                    @click="handleTableRowClick($event, student)">
                                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                        <div class="mdc-checkbox mdc-data-table__row-checkbox" :class="{ 'mdc-checkbox--selected': isStudentSelected(student.id) }">
                                            <input type="checkbox" class="mdc-checkbox__native-control row-checkbox" aria-label="Sélectionner la ligne"
                                                :checked="isStudentSelected(student.id)"
                                                @change="toggleStudentSelection(student.id)"
                                                @click.stop>
                                            <div class="mdc-checkbox__background">
                                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                                </svg>
                                                <div class="mdc-checkbox__mixedmark"></div>
                                            </div>
                                            <div class="mdc-checkbox__ripple"></div>
                                        </div>
                                    </td>
                                    <td class="mdc-data-table__cell">
                                        <div class="student-photo" :style="`background-image: url('${student.photo}')`"></div>
                                    </td>
                                    <th class="mdc-data-table__cell" scope="row">
                                        <div x-text="student.name"></div>
                                        <div x-show="student.name_ar" class="text-muted small" x-text="student.name_ar"></div>
                                    </th>
                                    <td class="mdc-data-table__cell" x-text="student.student_id || '-'"></td>
                                    <td class="mdc-data-table__cell" x-text="student.level_fr || '-'"></td>
                                    {% if is_arabic_school %}
                                    <td class="mdc-data-table__cell" x-text="student.level_ar || '-'"></td>
                                    {% endif %}
                                    <td class="mdc-data-table__cell" x-text="student.gender"></td>
                                    <td class="mdc-data-table__cell">
                                        <span class="status-badge" :class="student.status_class" x-text="student.status"></span>
                                    </td>
                                    <td class="mdc-data-table__cell numeric-cell amount-negative" x-text="formatAmount(student.debt)"></td>
                                    <td class="mdc-data-table__cell numeric-cell" x-text="formatAmount(student.amount)"></td>
                                    <td class="mdc-data-table__cell numeric-cell" :class="student.paid > 0 ? 'amount-positive' : ''" x-text="formatAmount(student.paid)"></td>
                                    <td class="mdc-data-table__cell numeric-cell" :class="student.remaining === 0 ? 'amount-positive' : (student.remaining > 0 ? 'amount-warning' : '')" x-text="student.remaining === 0 && student.amount > 0 ? 'Soldé' : formatAmount(student.remaining)"></td>
                                    <td class="mdc-data-table__cell">
                                        <div class="table-actions">
                                            <button class="action-btn edit-btn" title="Modifier l'élève" @click.stop="editStudent(student)">
                                                <span class="material-icons">edit</span>
                                            </button>
                                            <button class="action-btn payment-btn" title="Ajouter/Modifier un paiement" @click.stop="managePayment(student)">
                                                <span class="material-icons">payments</span>
                                            </button>
                                            <button class="action-btn delete-btn" title="Supprimer l'élève" @click.stop="deleteStudent(student)">
                                                <span class="material-icons">delete</span>
                                            </button>
                                            <button class="action-btn more-btn" title="Plus d'options" @click.stop="handleMoreAction($event, student)">
                                                <span class="material-icons">more_vert</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <!-- Pagination -->
                <div class="table-pagination">
                    <div class="pagination-info" id="pagination-info">
                        <span x-text="`Affichage ${((currentPage - 1) * perPage) + 1}-${Math.min(currentPage * perPage, filteredData.length)} sur ${filteredData.length} élèves`"></span>
                    </div>
                    <div class="pagination-controls">
                        <button class="mdc-icon-button"
                                :disabled="currentPage <= 1"
                                @click="prevPage()">
                            <span class="material-icons">chevron_left</span>
                        </button>
                        <div class="pagination-pages" id="pagination-pages">
                            <template x-for="page in visiblePages" :key="page">
                                <button class="pagination-page"
                                        :class="{ 'active': page === currentPage, 'ellipsis': page === '...' }"
                                        :disabled="page === '...'"
                                        @click="page !== '...' && goToPage(page)"
                                        x-text="page"></button>
                            </template>
                        </div>
                        <button class="mdc-icon-button"
                                :disabled="currentPage >= totalPages"
                                @click="nextPage()">
                            <span class="material-icons">chevron_right</span>
                        </button>
                    </div>
                </div>
            </div>

        </div>

        <!-- No Data Found Component -->
        <div class="no-data-found" x-show="filteredData.length === 0">
            <div class="no-data-content">
                <span class="material-icons no-data-icon">search_off</span>
                <div class="no-data-title">Aucun élève trouvé</div>
                <div class="no-data-message">Essayez d'ajuster vos critères de recherche ou de filtre</div>
                <button class="mdc-button mdc-button--outlined no-data-action" @click="clearAllFilters()">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Effacer les filtres</span>
                </button>
            </div>
        </div>

        <!-- Students List (Mobile) -->
        <div class="students-list">
            <!-- Targeted Loading Overlay for Mobile List -->
            <div class="target-loading-overlay" id="list-loading-overlay">
                <div class="target-loading-content">
                    <div class="simple-spinner">
                        <svg class="spinner-svg" viewBox="0 0 50 50">
                            <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#1976d2" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                    </div>
                    <div class="target-loading-text" id="list-loading-text">Chargement...</div>
                </div>
            </div>

            <!-- Alpine.js Mobile Students Template -->
            <template x-for="(student, index) in mobileDisplayData" :key="student.id">
                <div class="student-item"
                    :data-student-id="student.id"
                    @click="showBottomSheet({
                        id: student.id,
                        name: student.name,
                        level: student.level_fr,
                        details: `Matricule: ${student.student_id} • Sexe: ${student.gender}`,
                        photo: `url('${student.photo}')`
                    })">
                    <div class="student-photo" :style="`background-image: url('${student.photo}')`"></div>
                    <div class="student-info">
                        <div class="student-header">
                            <div class="student-name" x-text="student.name"></div>
                            <div class="student-id" x-text="student.student_id"></div>
                        </div>
                        <div class="student-name-ar" x-show="student.name_ar" x-text="student.name_ar"></div>
                        <div class="student-details">
                            <span x-text="student.gender"></span>
                            <span class="grade-separator">•</span>
                            <span class="status-badge" :class="student.status_class" x-text="student.status"></span>
                        </div>
                        <div class="student-grades">
                            <span class="grade-fr" x-text="student.level_fr || '-'"></span>
                            {% if is_arabic_school %}
                            <span class="grade-separator" x-show="student.level_ar">•</span>
                            <span class="grade-ar" x-show="student.level_ar" x-text="student.level_ar"></span>
                            {% endif %}
                        </div>
                        <div class="student-payments">
                            <span class="payment-info">
                                Payé: <span class="amount-positive" x-text="formatAmount(student.paid)"></span>
                            </span>
                            <span class="payment-separator">•</span>
                            <span class="payment-info">
                                Reste: <span :class="student.remaining === 0 ? 'amount-positive' : 'amount-warning'" x-text="student.remaining === 0 && student.amount > 0 ? 'Soldé' : formatAmount(student.remaining)"></span>
                            </span>
                        </div>
                    </div>
                    <div class="student-actions">
                        <button class="edit-btn" title="Modifier l'élève" @click.stop="editStudent(student)">
                            <span class="material-icons">edit</span>
                        </button>
                        <button class="payment-btn" title="Gérer les paiements" @click.stop="managePayment(student)">
                            <span class="material-icons">payments</span>
                        </button>
                    </div>
                </div>
            </template>

            <!-- Loading More Spinner Before New Content -->
            <div class="loading-more-spinner" x-show="isLoadingMore && hasMoreMobileData">
                <div class="spinner-container">
                    <div class="mdc-circular-progress mdc-circular-progress--small mdc-circular-progress--indeterminate" role="progressbar" aria-label="Chargement de plus d'élèves...">
                        <div class="mdc-circular-progress__determinate-container">
                            <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <circle class="mdc-circular-progress__determinate-track" cx="12" cy="12" r="8.5" stroke-width="2.5"/>
                                <circle class="mdc-circular-progress__determinate-circle" cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="53.407" stroke-width="2.5"/>
                            </svg>
                        </div>
                        <div class="mdc-circular-progress__indeterminate-container">
                            <div class="mdc-circular-progress__spinner-layer">
                                <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                                    <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="26.704" stroke-width="2.5"/>
                                    </svg>
                                </div>
                                <div class="mdc-circular-progress__gap-patch">
                                    <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="26.704" stroke-width="2"/>
                                    </svg>
                                </div>
                                <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                                    <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="26.704" stroke-width="2.5"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="loading-more-text">Chargement de plus d'élèves...</div>
            </div>

            <!-- End of Results Message -->
            <div class="end-of-results" x-show="!hasMoreMobileData && mobileDisplayData.length > 0">
                <div class="end-of-results-content">
                    <span class="material-icons">check_circle</span>
                    <div class="end-of-results-text">Vous avez atteint la fin de la liste</div>
                    <div class="end-of-results-count" x-text="`Affichage de tous les ${filteredData.length} élèves`"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize Django students data for Alpine.js
window.djangoStudentsData = [
    {% for enrollment in enrollments %}
    {
        id: {{ enrollment.id }},
        enrollment_id: {{ enrollment.id }},
        name: "{{ enrollment.student.get_full_name|escapejs }}",
        name_ar: "{{ enrollment.student.full_name_ar|default:''|escapejs }}",
        student_id: "{{ enrollment.student.student_id|default:''|escapejs }}",
        level_fr: "{{ enrollment.level_fr|default:''|escapejs }}",
        {% if is_arabic_school %}
        level_ar: "{{ enrollment.level_ar|default:''|escapejs }}",
        {% endif %}
        gender: "{{ enrollment.student.gender|escapejs }}",
        status: "{{ enrollment.status|default:'Actif'|escapejs }}",
        status_class: "{% if enrollment.active %}status-active{% else %}status-inactive{% endif %}",
        debt: {{ enrollment.debt|default:0 }},
        amount: {{ enrollment.amount|default:0 }},
        paid: {{ enrollment.paid|default:0 }},
        remaining: {{ enrollment.remaining|default:0 }},
        photo: "{% if enrollment.student.photo %}{{ enrollment.student.photo.url }}{% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}{{ enrollment.student.government_photo }}{% else %}{{ enrollment.student.blank_photo }}{% endif %}",
        date_added: "{{ enrollment.created_at|date:'Y-m-d' }}"
    }{% if not forloop.last %},{% endif %}
    {% endfor %}
];

// Alpine.js Students App
function studentsApp() {
    return {
        // Student Data - will be populated from Django template
        studentsData: [],

        // Mobile lazy loading properties
        mobileDisplayCount: 20, // Initial number of items to show on mobile
        mobileLoadIncrement: 10, // Number of items to load when scrolling
        isLoadingMore: false,

        // State variables
        currentPage: 1,
        perPage: 10,
        sortColumn: '',
        sortDirection: '',
        searchQuery: '',
        selectedStudentIds: new Set(),
        quickFilter: 'all', // Quick filter state

        // Initialize students data from Django template
        initializeStudentsData() {
            // This will be populated by Django template data
            this.studentsData = window.djangoStudentsData || [];
        },

        // Computed properties
        get filteredData() {
            return this.studentsData.filter(student => {
                // Quick filter
                let matchesQuickFilter = true;
                if (this.quickFilter !== 'all') {
                    switch (this.quickFilter) {
                        case 'paid':
                            matchesQuickFilter = student.paid > 0 && student.remaining > 0;
                            break;
                        case 'no-payments':
                            matchesQuickFilter = student.paid === 0;
                            break;
                        case 'all-paid':
                            matchesQuickFilter = student.remaining === 0;
                            break;
                        case 'enrolled-today':
                            const today = new Date().toISOString().split('T')[0];
                            matchesQuickFilter = student.date_added === today;
                            break;
                    }
                }

                // Search filter
                const matchesSearch = !this.searchQuery ||
                    student.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                    (student.student_id && student.student_id.toLowerCase().includes(this.searchQuery.toLowerCase())) ||
                    (student.level_fr && student.level_fr.toLowerCase().includes(this.searchQuery.toLowerCase()));

                return matchesQuickFilter && matchesSearch;
            }).sort((a, b) => {
                if (!this.sortColumn) return 0;

                let aVal = a[this.sortColumn];
                let bVal = b[this.sortColumn];

                if (['debt', 'amount', 'paid', 'remaining'].includes(this.sortColumn)) {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                }

                if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        },

        get paginatedData() {
            const startIndex = (this.currentPage - 1) * this.perPage;
            const endIndex = startIndex + this.perPage;
            return this.filteredData.slice(startIndex, endIndex);
        },

        get totalPages() {
            return Math.ceil(this.filteredData.length / this.perPage);
        },

        get visiblePages() {
            const total = this.totalPages;
            const current = this.currentPage;

            if (total <= 7) {
                return Array.from({length: total}, (_, i) => i + 1);
            }

            const pages = [];
            pages.push(1);

            let start = Math.max(2, current - 2);
            let end = Math.min(total - 1, current + 2);

            if (current <= 4) {
                start = 2;
                end = Math.min(total - 1, 5);
            } else if (current >= total - 3) {
                start = Math.max(2, total - 4);
                end = total - 1;
            }

            if (start > 2) {
                pages.push('...');
            }

            for (let i = start; i <= end; i++) {
                pages.push(i);
            }

            if (end < total - 1) {
                pages.push('...');
            }

            if (total > 1) {
                pages.push(total);
            }

            return pages;
        },

        // Mobile lazy loading computed properties
        get mobileDisplayData() {
            return this.filteredData.slice(0, this.mobileDisplayCount);
        },

        get hasMoreMobileData() {
            return this.mobileDisplayCount < this.filteredData.length;
        },

        // Methods
        formatAmount(amount) {
            if (amount === null || amount === undefined || amount === 0) return '-';
            return new Intl.NumberFormat('fr-FR').format(amount);
        },

        sortData(column) {
            if (this.sortColumn === column) {
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                this.sortColumn = column;
                this.sortDirection = 'asc';
            }
        },

        getSortIcon(column) {
            if (this.sortColumn !== column) return 'unfold_more';
            return this.sortDirection === 'asc' ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
        },

        getSortClass(column) {
            if (this.sortColumn !== column) return '';
            return this.sortDirection === 'asc' ? 'sort-asc' : 'sort-desc';
        },

        handleTableRowClick(event, student) {
            // Don't trigger if action buttons were clicked
            if (event.target.closest('.table-actions')) return;

            // Check if checkbox was clicked
            const checkbox = event.target.closest('.mdc-data-table__cell--checkbox');
            if (checkbox) {
                const checkboxInput = checkbox.querySelector('.row-checkbox');
                if (checkboxInput) {
                    checkboxInput.checked = !checkboxInput.checked;
                    this.toggleStudentSelection(student.id);
                }
                return;
            }

            // Show student details (could open a modal or navigate)
            console.log('Show student details:', student);
        },

        setQuickFilter(filterType) {
            // Update active chip styling
            document.querySelectorAll('.quick-filter-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');

            // Set the filter
            this.quickFilter = filterType;
            this.currentPage = 1; // Reset to first page when filtering
        },

        clearAllFilters() {
            this.searchQuery = '';
            this.quickFilter = 'all';
            this.currentPage = 1;

            // Update quick filter chip styling
            document.querySelectorAll('.quick-filter-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            document.querySelector('[data-filter="all"]').classList.add('active');

            // Clear search input
            const searchInput = document.getElementById('table-search');
            if (searchInput) {
                searchInput.value = '';
            }
        },

        toggleStudentSelection(studentId) {
            if (this.selectedStudentIds.has(studentId)) {
                this.selectedStudentIds.delete(studentId);
            } else {
                this.selectedStudentIds.add(studentId);
            }
        },

        isStudentSelected(studentId) {
            return this.selectedStudentIds.has(studentId);
        },

        selectAllStudents() {
            const allVisible = this.paginatedData.every(student => this.selectedStudentIds.has(student.id));
            if (allVisible) {
                // Deselect all visible students
                this.paginatedData.forEach(student => {
                    this.selectedStudentIds.delete(student.id);
                });
            } else {
                // Select all visible students
                this.paginatedData.forEach(student => {
                    this.selectedStudentIds.add(student.id);
                });
            }
        },

        get allVisibleSelected() {
            return this.paginatedData.length > 0 && this.paginatedData.every(student => this.selectedStudentIds.has(student.id));
        },

        get someVisibleSelected() {
            return this.paginatedData.some(student => this.selectedStudentIds.has(student.id));
        },

        // Pagination methods
        goToPage(page) {
            if (page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },

        prevPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },

        // Mobile lazy loading methods
        loadMoreStudents() {
            if (this.isLoadingMore || !this.hasMoreMobileData) return;

            this.isLoadingMore = true;

            setTimeout(() => {
                this.mobileDisplayCount += this.mobileLoadIncrement;
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.isLoadingMore = false;
                    }, 100);
                });
            }, 150);
        },

        resetMobileDisplay() {
            this.mobileDisplayCount = 20;
            this.isLoadingMore = false;
        },

        // Action methods
        editStudent(student) {
            // Trigger HTMX request to edit student
            const url = `{% url 'school:student_edit' 0 %}`.replace('0', student.enrollment_id);
            htmx.ajax('GET', url, {target: '#dialog-xl'});
        },

        managePayment(student) {
            // Trigger HTMX request to manage payments
            const url = `{% url 'school:payment_add' %}?student_id=${student.student_id}`;
            htmx.ajax('GET', url, {target: '#dialog-xl'});
        },

        deleteStudent(student) {
            // Show confirmation dialog
            if (confirm(`Êtes-vous sûr de vouloir supprimer ${student.name} ? Cette action ne peut pas être annulée.`)) {
                const url = `{% url 'school:student_delete' 0 %}`.replace('0', student.enrollment_id);
                htmx.ajax('GET', url, {target: '#dialog'});
            }
        },

        handleMoreAction(event, student) {
            // Show more options (could be a dropdown or bottom sheet)
            console.log('More actions for:', student.name);
        },

        showBottomSheet(studentData) {
            // Show bottom sheet with student data (mobile)
            console.log('Show bottom sheet for:', studentData);
        }
    }
}
</script>
