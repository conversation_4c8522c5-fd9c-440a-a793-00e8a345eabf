{% load humanize %}

<div class="content-header">
    <span class="material-icons">arrow_back</span>
    <h2 class="page-title">
        <span>{{ title|default:"Gestion des élèves" }}</span>
        <span class="page-title-count" x-show="activeNav === 'students'" x-text="`(${filteredData.length})`"></span>
    </h2>
    <div class="actions">
        <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn">
            <span class="mdc-button__ripple"></span>
            <span class="material-icons" style="pointer-events: none;">add</span>
            <span class="mdc-button__label">Ajouter un élève</span>
        </button>
        <span class="material-icons hidden" title="Exporter vers Excel">description</span>
        <span class="material-icons hidden" title="Importer des données">file_upload</span>
        <span class="material-icons hidden" title="Changer de vue">view_module</span>
        <span class="material-icons search-icon-mobile" title="Rechercher" id="mobile-search-btn">search</span>
        <span class="material-icons" title="Filtrer" id="filter-btn">filter_list</span>
    </div>
</div>

{% include partial_template %}
