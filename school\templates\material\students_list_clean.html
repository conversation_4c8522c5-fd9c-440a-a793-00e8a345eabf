{% load humanize %}
<div class="content-area" id="content-area">
    <div class="content-header" id="content-header">
        <span class="material-icons">arrow_back</span>
        <h2 class="page-title">
            <span x-text="pageTitle"></span>
        </h2>
        <div class="actions">
            <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn">
                <span class="mdc-button__ripple"></span>
                <span class="material-icons" style="pointer-events: none;">add</span>
                <span class="mdc-button__label">Ajouter un élève</span>
            </button>
            <span class="material-icons hidden" title="Exporter vers Excel">description</span>
            <span class="material-icons hidden" title="Importer des données">file_upload</span>
            <span class="material-icons hidden" title="Changer de vue">view_module</span>
            <span class="material-icons search-icon-mobile" title="Rechercher" id="mobile-search-btn">search</span>
            <span class="material-icons" title="Filtrer" id="filter-btn">filter_list</span>
        </div>
    </div>

    <div class="data-table-container">
        <!-- Table Controls -->
        <div class="table-controls">
            <div class="per-page-container">
                <span class="per-page-label">Lignes par page:</span>
                <div class="mdc-select mdc-select--outlined mdc-small-input" id="per-page-select">
                    <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                        <span class="mdc-select__selected-text-container">
                            <span class="mdc-select__selected-text">10</span>
                        </span>
                        <span class="mdc-select__dropdown-icon">
                            <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                            </svg>
                        </span>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                    <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                        <ul class="mdc-deprecated-list" role="listbox">
                            <li class="mdc-deprecated-list-item" data-value="5" role="option">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">5</span>
                            </li>
                            <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="10" role="option" aria-selected="true">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">10</span>
                            </li>
                            <li class="mdc-deprecated-list-item" data-value="25" role="option">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">25</span>
                            </li>
                            <li class="mdc-deprecated-list-item" data-value="50" role="option">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">50</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- MDC Data Table -->
        <div class="mdc-data-table">
            <div class="mdc-data-table__table-container">
                <table class="mdc-data-table__table" aria-label="Liste des élèves">
                    <thead>
                        <tr class="mdc-data-table__header-row">
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-checkbox" aria-label="Sélectionner toutes les lignes">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Photo</th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Nom et Prénoms</th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Matricule</th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Classe</th>
                            {% if is_arabic_school %}
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Arabe</th>
                            {% endif %}
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Sexe</th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Statut</th>
                            <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">Arriéré</th>
                            <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">À payer</th>
                            <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">Payé</th>
                            <th class="mdc-data-table__header-cell numeric-column" role="columnheader" scope="col">Reste</th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="mdc-data-table__content" id="table-body">
                        {% for enrollment in enrollments %}
                        <tr class="mdc-data-table__row table-row" data-student-id="{{ enrollment.id }}">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control row-checkbox" aria-label="Sélectionner la ligne">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </td>
                            <td class="mdc-data-table__cell">
                                <div class="student-photo" style="background-image: url('{% if enrollment.student.photo %}{{ enrollment.student.photo.url }}{% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}{{ enrollment.student.government_photo }}{% else %}{{ enrollment.student.blank_photo }}{% endif %}')"></div>
                            </td>
                            <th class="mdc-data-table__cell" scope="row">
                                <div>{{ enrollment.student.get_full_name }}</div>
                                {% if enrollment.student.full_name_ar %}
                                <div class="text-muted small">{{ enrollment.student.full_name_ar }}</div>
                                {% endif %}
                            </th>
                            <td class="mdc-data-table__cell">{{ enrollment.student.student_id|default:'-' }}</td>
                            <td class="mdc-data-table__cell">{{ enrollment.level_fr|default:'-' }}</td>
                            {% if is_arabic_school %}
                            <td class="mdc-data-table__cell">{{ enrollment.level_ar|default:'-' }}</td>
                            {% endif %}
                            <td class="mdc-data-table__cell">{{ enrollment.student.gender }}</td>
                            <td class="mdc-data-table__cell">
                                <span class="status-badge {% if enrollment.active %}status-active{% else %}status-inactive{% endif %}">
                                    {{ enrollment.status|default:'Actif' }}
                                </span>
                            </td>
                            <td class="mdc-data-table__cell numeric-cell amount-negative">{{ enrollment.debt|floatformat:0|default:'-' }}</td>
                            <td class="mdc-data-table__cell numeric-cell">{{ enrollment.amount|floatformat:0|default:'-' }}</td>
                            <td class="mdc-data-table__cell numeric-cell {% if enrollment.paid > 0 %}amount-positive{% endif %}">{{ enrollment.paid|floatformat:0|default:'-' }}</td>
                            <td class="mdc-data-table__cell numeric-cell {% if enrollment.remaining == 0 and enrollment.amount > 0 %}amount-positive{% elif enrollment.remaining > 0 %}amount-warning{% endif %}">
                                {% if enrollment.remaining == 0 and enrollment.amount > 0 %}
                                    Soldé
                                {% else %}
                                    {{ enrollment.remaining|floatformat:0|default:'-' }}
                                {% endif %}
                            </td>
                            <td class="mdc-data-table__cell">
                                <div class="table-actions">
                                    <button class="action-btn edit-btn" title="Modifier l'élève">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn payment-btn" title="Ajouter/Modifier un paiement">
                                        <span class="material-icons">payments</span>
                                    </button>
                                    <button class="action-btn delete-btn" title="Supprimer l'élève">
                                        <span class="material-icons">delete</span>
                                    </button>
                                    <button class="action-btn more-btn" title="Plus d'options">
                                        <span class="material-icons">more_vert</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell" colspan="13" style="text-align: center; padding: 48px;">
                                <div style="color: #757575;">
                                    <span class="material-icons" style="font-size: 48px; margin-bottom: 16px; display: block;">search_off</span>
                                    Aucun élève trouvé
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize MDC Select component
document.addEventListener('DOMContentLoaded', function() {
    const selectElement = document.getElementById('per-page-select');
    if (selectElement && typeof mdc !== 'undefined') {
        const select = new mdc.select.MDCSelect(selectElement);
    }
});
</script>
