// Alpine.js Students App
function studentsApp() {
    return {
        // Student Data - will be populated from Django template
        studentsData: [],

        // Mobile lazy loading properties
        mobileDisplayCount: 20, // Initial number of items to show on mobile
        mobileLoadIncrement: 10, // Number of items to load when scrolling
        isLoadingMore: false,

        // State variables
        currentPage: 1,
        perPage: 10,
        sortColumn: '',
        sortDirection: '',
        searchQuery: '',
        selectedStudentIds: new Set(),
        quickFilter: 'all', // Quick filter state

        // Initialize students data from Django template
        initializeStudentsData() {
            // This will be populated by Django template data
            this.studentsData = window.djangoStudentsData || [];
        },

        // Computed properties
        get filteredData() {
            return this.studentsData.filter(student => {
                // Quick filter
                let matchesQuickFilter = true;
                if (this.quickFilter !== 'all') {
                    switch (this.quickFilter) {
                        case 'paid':
                            matchesQuickFilter = student.paid > 0 && student.remaining > 0;
                            break;
                        case 'no-payments':
                            matchesQuickFilter = student.paid === 0;
                            break;
                        case 'all-paid':
                            matchesQuickFilter = student.remaining === 0;
                            break;
                        case 'enrolled-today':
                            const today = new Date().toISOString().split('T')[0];
                            matchesQuickFilter = student.date_added === today;
                            break;
                    }
                }

                // Search filter
                const matchesSearch = !this.searchQuery ||
                    student.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                    (student.student_id && student.student_id.toLowerCase().includes(this.searchQuery.toLowerCase())) ||
                    (student.level_fr && student.level_fr.toLowerCase().includes(this.searchQuery.toLowerCase()));

                return matchesQuickFilter && matchesSearch;
            }).sort((a, b) => {
                if (!this.sortColumn) return 0;

                let aVal = a[this.sortColumn];
                let bVal = b[this.sortColumn];

                if (['debt', 'amount', 'paid', 'remaining'].includes(this.sortColumn)) {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                }

                if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
                return 0;
            });
        },

        get paginatedData() {
            const startIndex = (this.currentPage - 1) * this.perPage;
            const endIndex = startIndex + this.perPage;
            return this.filteredData.slice(startIndex, endIndex);
        },

        get totalPages() {
            return Math.ceil(this.filteredData.length / this.perPage);
        },

        get visiblePages() {
            const total = this.totalPages;
            const current = this.currentPage;

            if (total <= 7) {
                return Array.from({length: total}, (_, i) => i + 1);
            }

            const pages = [];
            pages.push(1);

            let start = Math.max(2, current - 2);
            let end = Math.min(total - 1, current + 2);

            if (current <= 4) {
                start = 2;
                end = Math.min(total - 1, 5);
            } else if (current >= total - 3) {
                start = Math.max(2, total - 4);
                end = total - 1;
            }

            if (start > 2) {
                pages.push('...');
            }

            for (let i = start; i <= end; i++) {
                pages.push(i);
            }

            if (end < total - 1) {
                pages.push('...');
            }

            if (total > 1) {
                pages.push(total);
            }

            return pages;
        },

        // Mobile lazy loading computed properties
        get mobileDisplayData() {
            return this.filteredData.slice(0, this.mobileDisplayCount);
        },

        get hasMoreMobileData() {
            return this.mobileDisplayCount < this.filteredData.length;
        },

        // Methods
        formatAmount(amount) {
            if (amount === null || amount === undefined || amount === 0) return '-';
            return new Intl.NumberFormat('fr-FR').format(amount);
        },

        sortData(column) {
            if (this.sortColumn === column) {
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                this.sortColumn = column;
                this.sortDirection = 'asc';
            }
        },

        getSortIcon(column) {
            if (this.sortColumn !== column) return 'unfold_more';
            return this.sortDirection === 'asc' ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
        },

        getSortClass(column) {
            if (this.sortColumn !== column) return '';
            return this.sortDirection === 'asc' ? 'sort-asc' : 'sort-desc';
        },

        handleTableRowClick(event, student) {
            // Don't trigger if action buttons were clicked
            if (event.target.closest('.table-actions')) return;

            // Check if checkbox was clicked
            const checkbox = event.target.closest('.mdc-data-table__cell--checkbox');
            if (checkbox) {
                const checkboxInput = checkbox.querySelector('.row-checkbox');
                if (checkboxInput) {
                    checkboxInput.checked = !checkboxInput.checked;
                    this.toggleStudentSelection(student.id);
                }
                return;
            }

            // Show student details (could open a modal or navigate)
            console.log('Show student details:', student);
        },

        setQuickFilter(filterType) {
            // Update active chip styling
            document.querySelectorAll('.quick-filter-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');

            // Set the filter
            this.quickFilter = filterType;
            this.currentPage = 1; // Reset to first page when filtering
        },

        clearAllFilters() {
            this.searchQuery = '';
            this.quickFilter = 'all';
            this.currentPage = 1;

            // Update quick filter chip styling
            document.querySelectorAll('.quick-filter-chip').forEach(chip => {
                chip.classList.remove('active');
            });
            document.querySelector('[data-filter="all"]').classList.add('active');

            // Clear search input
            const searchInput = document.getElementById('table-search');
            if (searchInput) {
                searchInput.value = '';
            }
        },

        toggleStudentSelection(studentId) {
            if (this.selectedStudentIds.has(studentId)) {
                this.selectedStudentIds.delete(studentId);
            } else {
                this.selectedStudentIds.add(studentId);
            }
        },

        isStudentSelected(studentId) {
            return this.selectedStudentIds.has(studentId);
        },

        selectAllStudents() {
            const allVisible = this.paginatedData.every(student => this.selectedStudentIds.has(student.id));
            if (allVisible) {
                // Deselect all visible students
                this.paginatedData.forEach(student => {
                    this.selectedStudentIds.delete(student.id);
                });
            } else {
                // Select all visible students
                this.paginatedData.forEach(student => {
                    this.selectedStudentIds.add(student.id);
                });
            }
        },

        get allVisibleSelected() {
            return this.paginatedData.length > 0 && this.paginatedData.every(student => this.selectedStudentIds.has(student.id));
        },

        get someVisibleSelected() {
            return this.paginatedData.some(student => this.selectedStudentIds.has(student.id));
        },

        // Pagination methods
        goToPage(page) {
            if (page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },

        prevPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },

        // Mobile lazy loading methods
        loadMoreStudents() {
            if (this.isLoadingMore || !this.hasMoreMobileData) return;

            this.isLoadingMore = true;

            setTimeout(() => {
                this.mobileDisplayCount += this.mobileLoadIncrement;
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.isLoadingMore = false;
                    }, 100);
                });
            }, 150);
        },

        resetMobileDisplay() {
            this.mobileDisplayCount = 20;
            this.isLoadingMore = false;
        },

        // Action methods
        editStudent(student) {
            // Trigger HTMX request to edit student
            const url = `{% url 'school:student_edit' 0 %}`.replace('0', student.enrollment_id);
            htmx.ajax('GET', url, {target: '#dialog-xl'});
        },

        managePayment(student) {
            // Trigger HTMX request to manage payments
            const url = `{% url 'school:payment_add' %}?student_id=${student.student_id}`;
            htmx.ajax('GET', url, {target: '#dialog-xl'});
        },

        deleteStudent(student) {
            // Show confirmation dialog
            if (confirm(`Êtes-vous sûr de vouloir supprimer ${student.name} ? Cette action ne peut pas être annulée.`)) {
                const url = `{% url 'school:student_delete' 0 %}`.replace('0', student.enrollment_id);
                htmx.ajax('GET', url, {target: '#dialog'});
            }
        },

        handleMoreAction(event, student) {
            // Show more options (could be a dropdown or bottom sheet)
            console.log('More actions for:', student.name);
        },

        showBottomSheet(studentData) {
            // Show bottom sheet with student data (mobile)
            console.log('Show bottom sheet for:', studentData);
        }
    }
}
